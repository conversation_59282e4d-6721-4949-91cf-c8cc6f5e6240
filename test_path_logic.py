#!/usr/bin/env python3

"""
Test script to verify the path logic edge cases
"""

from pathlib import Path

def test_path_logic():
    """Test various edge cases for the path construction logic"""
    
    # Simulate different subvolume filesystem paths
    test_cases = [
        # (subvolume_filesystem_path, boot_file_path, expected_result_path)
        (Path("/"), "/boot/vmlinuz-linux", Path("/boot/vmlinuz-linux")),
        (Path("/"), "/@/boot/vmlinuz-linux", Path("/@/boot/vmlinuz-linux")),
        (Path("/mnt/btrfs"), "/boot/vmlinuz-linux", Path("/mnt/btrfs/boot/vmlinuz-linux")),
        (Path("/mnt/btrfs"), "/@/boot/vmlinuz-linux", Path("/mnt/btrfs/@/boot/vmlinuz-linux")),
        (Path("/btrfs_root/timeshift-btrfs/snapshots/2025-09-23_22-00-00/@"), "/boot/vmlinuz-linux", 
         Path("/btrfs_root/timeshift-btrfs/snapshots/2025-09-23_22-00-00/@/boot/vmlinuz-linux")),
    ]
    
    print("Testing path construction logic:")
    print("=" * 60)
    
    for i, (subvolume_path, boot_file_path, expected) in enumerate(test_cases, 1):
        try:
            # This is the logic from our fix
            full_path_to_check = subvolume_path / Path(boot_file_path).relative_to('/')
            
            print(f"Test {i}:")
            print(f"  Subvolume path: {subvolume_path}")
            print(f"  Boot file path: {boot_file_path}")
            print(f"  Expected: {expected}")
            print(f"  Actual:   {full_path_to_check}")
            print(f"  Match: {'✅' if full_path_to_check == expected else '❌'}")
            print()
            
        except Exception as e:
            print(f"Test {i} FAILED with exception: {e}")
            print(f"  Subvolume path: {subvolume_path}")
            print(f"  Boot file path: {boot_file_path}")
            print()

def test_edge_cases():
    """Test potential edge cases that might cause issues"""
    
    print("Testing edge cases:")
    print("=" * 60)
    
    edge_cases = [
        # Paths that might cause issues
        ("", "Empty boot file path"),
        ("boot/vmlinuz-linux", "Relative path (no leading /)"),
        ("//boot//vmlinuz-linux", "Double slashes"),
        ("/boot/../boot/vmlinuz-linux", "Path with ..")
    ]
    
    subvolume_path = Path("/")
    
    for i, (boot_file_path, description) in enumerate(edge_cases, 1):
        try:
            print(f"Edge case {i}: {description}")
            print(f"  Boot file path: '{boot_file_path}'")
            
            if boot_file_path == "":
                print("  Result: Empty path - would likely be filtered out earlier")
            elif not boot_file_path.startswith("/"):
                print("  Result: Relative path - would cause ValueError in relative_to('/')")
            else:
                full_path_to_check = subvolume_path / Path(boot_file_path).relative_to('/')
                print(f"  Result: {full_path_to_check}")
            print()
            
        except Exception as e:
            print(f"  Exception: {e}")
            print()

if __name__ == "__main__":
    test_path_logic()
    test_edge_cases()
