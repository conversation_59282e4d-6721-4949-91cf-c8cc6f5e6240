#######################
## refind-btrfs.conf ##
#######################

# TOML syntax

# esp_uuid = <string>
## Explicitly defined ESP's Part-UUID which can be used in case the ESP itself
## cannot be automatically located on the system (for whatever reason).
## This option is, by default, defined as an empty UUID which means that it is
## ignored.

esp_uuid = "00000000-0000-0000-0000-000000000000"

# exit_if_root_is_snapshot = <bool>
## Whether to issue a warning and prematurely exit in case the root partition
## is already mounted as a snapshot.
## WARNING: Disabling this option is considered experimental and may result in
## unstable and/or erroneous behavior.

exit_if_root_is_snapshot = true

# exit_if_no_changes_are_detected = <bool>
## Whether to issue a warning and prematurely exit in case no changes were
## detected by comparing the preparation results of the current run with those
## of the previous run (if it exists).
## Changes are considered to be detected in case any of the following
## conditions are satisfied:
##      • this configuration file was modified
##      • rEFInd's configuration file (main or included) was modified
##      • at least one snapshot was found (either for addition or removal)
## The time of last modification (st_mtime) is used to detect file changes
## instead of comparing their contents.

exit_if_no_changes_are_detected = true

# [[snapshot-search]]
## Array of objects used to configure the behavior of searching for snapshots.
## The directory (or directories) listed in this array (including nested
## directories, up to "max_depth" - 1) are also watched for changes by the
## background running mode.
#
# directory = <string>
## Directory in which to search for snapshots (absolute filesystem path).
## WARNING: This directory must not be the same as or nested in the directory
## defined by the "destination_dir" option (shown further below).
#
# is_nested = <bool>
## Whether to search for snapshots nested within another snapshot. Only one
## level of nesting is supported and a search is performed in the same
## directory (if it exists) that is, in this context, relative to the found
## snapshot's root directory instead of the system's root directory. The same
## maximum search depth is used, as well.
## Setting this option to "false" potentially also means stopping the search
## prematurely (i.e., before the maximum search depth was ever reached) in
## those branches in which a snapshot was found.
#
# max_depth = <int>
## Maximum search depth relative to the search directory.
## WARNING: Defining a large value can seriously impact performance (of both
## searching for snapshots and watching for directory changes) in case the tree
## (whose root is the search directory) is sufficiently large (deep and/or
## wide).

[[snapshot-search]]
directory = "/.snapshots"
is_nested = false
max_depth = 2

# [snapshot-manipulation]
## Object used to configure the behavior of preparatory steps required
## to enable booting into snapshots as well as deleting those that aren't
## needed anymore.
#
# selection_count = <int> or <string>
## Number of snapshots (sorted descending by creation time) to include or
## "inf" to always include every currently present snapshot.
#
# modify_read_only_flag = <bool>
## Whether to change the read-only flag of a snapshot instead of creating
## a new writable snapshot from it. This option has no meaning for those
## snapshots that are already writable.
#
# destination_directory  = <string>
## Directory in which writable snapshots are to be placed (absolute filesystem
## path). This option has no meaning in case the "modify_read_only_flag" option
## is set to "true". It needn't exist beforehand as it is created in case it
## doesn't (including its missing parents, if any).
## WARNING: This directory must not be the same as or nested in an ony of the
## snapshot search directories.
#
# cleanup_exclusion = <array<string>>
## Array comprised of UUIDs (duplicates are ignored) of previously
## created writable snapshots that are to be excluded during automatic cleanup.
## These snapshots will not be deleted and should always appear as part of a
## generated boot stanza.
## See the output of "btrfs subvolume show <snapshot-filesystem-path>" for
## the expected format (shown in the "UUID" column). Same remark applies here
## with regards to the "modify_read_only_flag" option.

[snapshot-manipulation]
selection_count = 5
modify_read_only_flag = false
destination_directory = "/root/.refind-btrfs"
cleanup_exclusion = []

# [boot-stanza-generation]
## Object used to configure the process of combining the source boot stanza
## with previously prepared snapshots into a generated boot stanza.
#
# refind_config = <string>
## Name of rEFInd's main configuration file which must reside somewhere on
## the ESP. This option must not be defined as a path (neither absolute nor
## relative).
#
# include_paths = <bool>
## Whether to adjust the "loader" and "initrd" paths found in the source boot
## stanza. Setting this option to "true" while having a separate /boot
## partition has no meaning and is ignored.
#
# include_sub_menus = <bool>
## Whether to include sub-menus ("submenuentry") defined as part of the source
## boot stanza in the generated boot stanza. If set to "true", only those
## sub-menus which do not override the main stanza's "loader" and "options"
## fields and which do not delete (i.e., set it to nothing) its "initrd" field
## are taken into consideration.
## WARNING: Enabling this option in combination with setting a large
## "selection_count" value (greater than 10, for example) or, worse yet, by
## setting it to "inf" can potentially result in an overcrowded "Boot Options"
## menu.
#
## source_exclusion = <array<string>>
## Array comprised of loader filenames ("loader") with which the matched source
## boot stanzas can be arbitrarily excluded from processing, i.e., these boot
## stanzas will not be taken into account during the generation phase.
## For example, it can be defined as: ["vmlinuz-linux", "vmlinuz-linux-lts"].
## WARNING: This array must not contain all of the matched source boot stanza's
## loader filenames. If it does, an error is issued and a premature exit is
## performed.
## Also, a manual cleanup of the generated boot stanza (or stanzas) and its
## inclusion within the rEFInd's main configuration file is required in case
## the array's members were defined after the fact.

[boot-stanza-generation]
refind_config = "refind.conf"
include_paths = true
include_sub_menus = false
source_exclusion = []

# [boot-stanza-generation.icon]
## Subobject used to configure the process of defining the generated boot
## stanza's icon.
#
# mode = <string>
## Selected mode of icon generation which can be defined as one of:
##      • "default" - the source boot stanza's icon is reused, as is
##      • "custom" - a user provided image file path is used as the icon
##      • "embed_btrfs_logo" - the Btrfs logo is embedded into the source boot
##                             stanza's icon
#
## path = <string>
## Path of the user provided image file, relative to whichever directory the
## file defined by the "refind_config" option was found. This option is taken
## into consideration in case the "mode" option is set to "custom" but is
## otherwise ignored.
## WARNING: The format of the image located at this path must be one of those
## which rEFInd itself supports, that is one of the following: PNG, JPEG, BMP
## or ICNS.

[boot-stanza-generation.icon]
mode = "default"
path = "btrfs-snapshot-stanzas/icons/sample_icon.png"

# [boot-stanza-generation.icon.btrfs-logo]
## Subobject used to configure the behavior of embedding the Btrfs logo into
## the source boot stanza's icon. It is taken into consideration in case the
## "mode" option is set to "embed_btrfs_logo" but is otherwise ignored.
## WARNING: The source boot stanza icon's format must be PNG and its dimensions
## (width or height) must exceed those defined by the "size" option.
#
# variant = <string>
## Btrfs logo variant to be used for embedding which can be defined as one of:
##      • "original" - dark variant, suitable for light themes (including the
##                     default theme)
##      • "inverted" - light variant created by inverting the original logo's
##                     pixels' color values, suitable for dark themes
#
# size = <string>
## Size of the chosen Btrfs logo's variant defined by the "type" option. Both
## variants come in three different sizes which should be a sufficiently
## flexible choice and as such suitable for a decent number of different OS
## icons, both default and custom. It can be defined as one of:
##      • "small" - 32x20 pixels
##      • "medium" - 48x30 pixels
##      • "large" - 64x40 pixels
#
# horizontal_alignment = <string>
## Horizontal alignment (x-axis) of the embedded Btrfs logo which can be
## defined as one of:
##      • "left"
##      • "center"
##      • "right"
#
# vertical_alignment = <string>
## Vertical alignment (y-axis) of the embedded Btrfs logo which can be defined
## as one of:
##      • "top"
##      • "center"
##      • "bottom"

[boot-stanza-generation.icon.btrfs-logo]
variant = "original"
size = "medium"
horizontal_alignment = "center"
vertical_alignment = "center"
