# region Licensing
# SPDX-FileCopyrightText: 2020-2024 <PERSON><PERSON> <<EMAIL>>
#
# SPDX-License-Identifier: GPL-3.0-or-later

""" refind-btrfs - Generate rEFInd manual boot stanzas from Btrfs snapshots
Copyright (C) 2020-2024 <PERSON><PERSON>

This program is free software: you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation, either version 3 of the License, or
(at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with this program.  If not, see <https://www.gnu.org/licenses/>.
"""
# endregion

import logging
from logging import Formatter, LogRecord

from refind_btrfs.common import constants


class LevelAwareFormatter(Formatter):
    def format(self, record: LogRecord) -> str:
        levelno = record.levelno
        fmt = constants.EMPTY_STR

        if levelno == logging.INFO:
            fmt = "%(message)s"
        elif levelno == logging.WARNING:
            fmt = "%(levelname)s: %(message)s"
        else:
            fmt = "%(levelname)s (%(name)s/%(filename)s/%(funcName)s): %(message)s"

        formatter = Formatter(fmt)

        return formatter.format(record)
