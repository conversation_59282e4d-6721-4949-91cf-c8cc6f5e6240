# Generated from c:/Users/<USER>/Projects/Python/refind-btrfs/src/refind_btrfs/boot/antlr4/RefindConfigLexer.g4 by ANTLR 4.13.1
from antlr4 import *
from io import StringIO
import sys
if sys.version_info[1] > 5:
    from typing import TextIO
else:
    from typing.io import TextIO


def serializedATN():
    return [
        4,0,23,1036,6,-1,6,-1,2,0,7,0,2,1,7,1,2,2,7,2,2,3,7,3,2,4,7,4,2,
        5,7,5,2,6,7,6,2,7,7,7,2,8,7,8,2,9,7,9,2,10,7,10,2,11,7,11,2,12,7,
        12,2,13,7,13,2,14,7,14,2,15,7,15,2,16,7,16,2,17,7,17,2,18,7,18,2,
        19,7,19,2,20,7,20,2,21,7,21,2,22,7,22,2,23,7,23,2,24,7,24,2,25,7,
        25,2,26,7,26,2,27,7,27,2,28,7,28,1,0,4,0,62,8,0,11,0,12,0,63,1,0,
        1,0,1,1,3,1,69,8,1,1,1,1,1,1,1,1,1,1,2,3,2,76,8,2,1,2,1,2,1,2,1,
        2,1,3,1,3,5,3,84,8,3,10,3,12,3,87,9,3,1,3,3,3,90,8,3,1,3,1,3,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,1,4,
        1,4,1,4,1,4,3,4,818,8,4,1,4,5,4,821,8,4,10,4,12,4,824,9,4,1,4,3,
        4,827,8,4,1,4,1,4,1,5,1,5,3,5,833,8,5,1,6,1,6,1,6,1,6,1,6,1,6,1,
        6,1,6,1,6,1,6,1,7,1,7,1,7,1,7,1,7,1,7,1,7,1,7,1,7,1,7,1,7,1,7,1,
        7,1,8,1,8,1,8,1,8,1,8,1,8,1,8,1,9,1,9,1,9,1,9,1,9,1,9,1,9,1,10,1,
        10,1,10,1,10,1,10,1,10,1,10,1,11,1,11,1,11,1,11,1,11,1,12,1,12,1,
        12,1,12,1,12,1,12,1,12,1,12,1,12,1,12,1,12,1,13,1,13,1,13,1,13,1,
        13,1,13,1,13,1,13,1,13,1,13,1,13,1,13,1,13,1,14,1,14,1,14,1,14,1,
        14,1,14,1,14,1,14,1,15,1,15,1,15,1,15,1,15,1,15,1,15,1,15,1,15,1,
        15,1,15,1,15,1,16,1,16,1,16,1,16,1,16,1,16,1,16,1,16,1,16,1,16,1,
        16,1,16,1,16,1,16,1,16,1,16,1,16,1,17,1,17,1,17,1,17,1,17,1,17,1,
        17,1,17,1,17,1,18,1,18,1,18,1,18,1,18,1,18,1,18,1,18,1,19,1,19,1,
        20,1,20,1,21,4,21,967,8,21,11,21,12,21,968,1,22,1,22,1,23,1,23,1,
        23,3,23,976,8,23,1,24,1,24,4,24,980,8,24,11,24,12,24,981,1,24,1,
        24,1,25,1,25,4,25,988,8,25,11,25,12,25,989,1,25,1,25,1,26,4,26,995,
        8,26,11,26,12,26,996,1,27,1,27,1,27,1,27,1,27,1,27,1,27,1,27,1,27,
        1,27,1,27,1,27,1,27,1,27,1,27,1,27,1,27,1,27,1,27,1,27,1,27,1,27,
        1,27,1,27,1,27,3,27,1024,8,27,1,27,1,27,1,28,1,28,1,28,1,28,1,28,
        3,28,1033,8,28,1,28,1,28,0,0,29,2,1,4,2,6,3,8,4,10,5,12,6,14,0,16,
        0,18,7,20,8,22,9,24,10,26,11,28,12,30,13,32,14,34,15,36,16,38,17,
        40,18,42,19,44,20,46,0,48,21,50,0,52,0,54,0,56,22,58,23,2,0,1,4,
        2,0,9,9,32,32,1,0,10,10,3,0,48,57,65,70,97,102,2,0,9,10,32,32,1098,
        0,2,1,0,0,0,0,4,1,0,0,0,0,6,1,0,0,0,0,8,1,0,0,0,0,10,1,0,0,0,0,12,
        1,0,0,0,0,18,1,0,0,0,0,20,1,0,0,0,0,22,1,0,0,0,0,24,1,0,0,0,0,26,
        1,0,0,0,0,28,1,0,0,0,0,30,1,0,0,0,0,32,1,0,0,0,0,34,1,0,0,0,0,36,
        1,0,0,0,0,38,1,0,0,0,0,40,1,0,0,0,0,42,1,0,0,0,0,44,1,0,0,0,0,48,
        1,0,0,0,1,56,1,0,0,0,1,58,1,0,0,0,2,61,1,0,0,0,4,68,1,0,0,0,6,75,
        1,0,0,0,8,81,1,0,0,0,10,817,1,0,0,0,12,832,1,0,0,0,14,834,1,0,0,
        0,16,844,1,0,0,0,18,857,1,0,0,0,20,864,1,0,0,0,22,871,1,0,0,0,24,
        878,1,0,0,0,26,883,1,0,0,0,28,894,1,0,0,0,30,907,1,0,0,0,32,915,
        1,0,0,0,34,927,1,0,0,0,36,944,1,0,0,0,38,953,1,0,0,0,40,961,1,0,
        0,0,42,963,1,0,0,0,44,966,1,0,0,0,46,970,1,0,0,0,48,975,1,0,0,0,
        50,977,1,0,0,0,52,985,1,0,0,0,54,994,1,0,0,0,56,1023,1,0,0,0,58,
        1032,1,0,0,0,60,62,7,0,0,0,61,60,1,0,0,0,62,63,1,0,0,0,63,61,1,0,
        0,0,63,64,1,0,0,0,64,65,1,0,0,0,65,66,6,0,0,0,66,3,1,0,0,0,67,69,
        5,13,0,0,68,67,1,0,0,0,68,69,1,0,0,0,69,70,1,0,0,0,70,71,5,10,0,
        0,71,72,1,0,0,0,72,73,6,1,0,0,73,5,1,0,0,0,74,76,3,2,0,0,75,74,1,
        0,0,0,75,76,1,0,0,0,76,77,1,0,0,0,77,78,3,4,1,0,78,79,1,0,0,0,79,
        80,6,2,0,0,80,7,1,0,0,0,81,85,5,35,0,0,82,84,8,1,0,0,83,82,1,0,0,
        0,84,87,1,0,0,0,85,83,1,0,0,0,85,86,1,0,0,0,86,89,1,0,0,0,87,85,
        1,0,0,0,88,90,3,4,1,0,89,88,1,0,0,0,89,90,1,0,0,0,90,91,1,0,0,0,
        91,92,6,3,0,0,92,9,1,0,0,0,93,94,5,97,0,0,94,95,5,108,0,0,95,96,
        5,115,0,0,96,97,5,111,0,0,97,98,5,95,0,0,98,99,5,115,0,0,99,100,
        5,99,0,0,100,101,5,97,0,0,101,102,5,110,0,0,102,103,5,95,0,0,103,
        104,5,100,0,0,104,105,5,105,0,0,105,106,5,114,0,0,106,818,5,115,
        0,0,107,108,5,97,0,0,108,109,5,108,0,0,109,110,5,115,0,0,110,111,
        5,111,0,0,111,112,5,95,0,0,112,113,5,115,0,0,113,114,5,99,0,0,114,
        115,5,97,0,0,115,116,5,110,0,0,116,117,5,95,0,0,117,118,5,116,0,
        0,118,119,5,111,0,0,119,120,5,111,0,0,120,121,5,108,0,0,121,818,
        5,115,0,0,122,123,5,98,0,0,123,124,5,97,0,0,124,125,5,110,0,0,125,
        126,5,110,0,0,126,127,5,101,0,0,127,818,5,114,0,0,128,129,5,98,0,
        0,129,130,5,97,0,0,130,131,5,110,0,0,131,132,5,110,0,0,132,133,5,
        101,0,0,133,134,5,114,0,0,134,135,5,95,0,0,135,136,5,115,0,0,136,
        137,5,99,0,0,137,138,5,97,0,0,138,139,5,108,0,0,139,818,5,101,0,
        0,140,141,5,98,0,0,141,142,5,105,0,0,142,143,5,103,0,0,143,144,5,
        95,0,0,144,145,5,105,0,0,145,146,5,99,0,0,146,147,5,111,0,0,147,
        148,5,110,0,0,148,149,5,95,0,0,149,150,5,115,0,0,150,151,5,105,0,
        0,151,152,5,122,0,0,152,818,5,101,0,0,153,154,5,99,0,0,154,155,5,
        115,0,0,155,156,5,114,0,0,156,157,5,95,0,0,157,158,5,118,0,0,158,
        159,5,97,0,0,159,160,5,108,0,0,160,161,5,117,0,0,161,162,5,101,0,
        0,162,818,5,115,0,0,163,164,5,100,0,0,164,165,5,101,0,0,165,166,
        5,102,0,0,166,167,5,97,0,0,167,168,5,117,0,0,168,169,5,108,0,0,169,
        170,5,116,0,0,170,171,5,95,0,0,171,172,5,115,0,0,172,173,5,101,0,
        0,173,174,5,108,0,0,174,175,5,101,0,0,175,176,5,99,0,0,176,177,5,
        116,0,0,177,178,5,105,0,0,178,179,5,111,0,0,179,818,5,110,0,0,180,
        181,5,100,0,0,181,182,5,111,0,0,182,183,5,110,0,0,183,184,5,39,0,
        0,184,185,5,116,0,0,185,186,5,95,0,0,186,187,5,115,0,0,187,188,5,
        99,0,0,188,189,5,97,0,0,189,190,5,110,0,0,190,191,5,95,0,0,191,192,
        5,100,0,0,192,193,5,105,0,0,193,194,5,114,0,0,194,818,5,115,0,0,
        195,196,5,100,0,0,196,197,5,111,0,0,197,198,5,110,0,0,198,199,5,
        39,0,0,199,200,5,116,0,0,200,201,5,95,0,0,201,202,5,115,0,0,202,
        203,5,99,0,0,203,204,5,97,0,0,204,205,5,110,0,0,205,206,5,95,0,0,
        206,207,5,102,0,0,207,208,5,105,0,0,208,209,5,108,0,0,209,210,5,
        101,0,0,210,818,5,115,0,0,211,212,5,100,0,0,212,213,5,111,0,0,213,
        214,5,110,0,0,214,215,5,39,0,0,215,216,5,116,0,0,216,217,5,95,0,
        0,217,218,5,115,0,0,218,219,5,99,0,0,219,220,5,97,0,0,220,221,5,
        110,0,0,221,222,5,95,0,0,222,223,5,102,0,0,223,224,5,105,0,0,224,
        225,5,114,0,0,225,226,5,109,0,0,226,227,5,119,0,0,227,228,5,97,0,
        0,228,229,5,114,0,0,229,818,5,101,0,0,230,231,5,100,0,0,231,232,
        5,111,0,0,232,233,5,110,0,0,233,234,5,39,0,0,234,235,5,116,0,0,235,
        236,5,95,0,0,236,237,5,115,0,0,237,238,5,99,0,0,238,239,5,97,0,0,
        239,240,5,110,0,0,240,241,5,95,0,0,241,242,5,116,0,0,242,243,5,111,
        0,0,243,244,5,111,0,0,244,245,5,108,0,0,245,818,5,115,0,0,246,247,
        5,100,0,0,247,248,5,111,0,0,248,249,5,110,0,0,249,250,5,39,0,0,250,
        251,5,116,0,0,251,252,5,95,0,0,252,253,5,115,0,0,253,254,5,99,0,
        0,254,255,5,97,0,0,255,256,5,110,0,0,256,257,5,95,0,0,257,258,5,
        118,0,0,258,259,5,111,0,0,259,260,5,108,0,0,260,261,5,117,0,0,261,
        262,5,109,0,0,262,263,5,101,0,0,263,818,5,115,0,0,264,265,5,100,
        0,0,265,266,5,111,0,0,266,267,5,110,0,0,267,268,5,116,0,0,268,269,
        5,95,0,0,269,270,5,115,0,0,270,271,5,99,0,0,271,272,5,97,0,0,272,
        273,5,110,0,0,273,274,5,95,0,0,274,275,5,100,0,0,275,276,5,105,0,
        0,276,277,5,114,0,0,277,818,5,115,0,0,278,279,5,100,0,0,279,280,
        5,111,0,0,280,281,5,110,0,0,281,282,5,116,0,0,282,283,5,95,0,0,283,
        284,5,115,0,0,284,285,5,99,0,0,285,286,5,97,0,0,286,287,5,110,0,
        0,287,288,5,95,0,0,288,289,5,102,0,0,289,290,5,105,0,0,290,291,5,
        108,0,0,291,292,5,101,0,0,292,818,5,115,0,0,293,294,5,100,0,0,294,
        295,5,111,0,0,295,296,5,110,0,0,296,297,5,116,0,0,297,298,5,95,0,
        0,298,299,5,115,0,0,299,300,5,99,0,0,300,301,5,97,0,0,301,302,5,
        110,0,0,302,303,5,95,0,0,303,304,5,102,0,0,304,305,5,105,0,0,305,
        306,5,114,0,0,306,307,5,109,0,0,307,308,5,119,0,0,308,309,5,97,0,
        0,309,310,5,114,0,0,310,818,5,101,0,0,311,312,5,100,0,0,312,313,
        5,111,0,0,313,314,5,110,0,0,314,315,5,116,0,0,315,316,5,95,0,0,316,
        317,5,115,0,0,317,318,5,99,0,0,318,319,5,97,0,0,319,320,5,110,0,
        0,320,321,5,95,0,0,321,322,5,116,0,0,322,323,5,111,0,0,323,324,5,
        111,0,0,324,325,5,108,0,0,325,818,5,115,0,0,326,327,5,100,0,0,327,
        328,5,111,0,0,328,329,5,110,0,0,329,330,5,116,0,0,330,331,5,95,0,
        0,331,332,5,115,0,0,332,333,5,99,0,0,333,334,5,97,0,0,334,335,5,
        110,0,0,335,336,5,95,0,0,336,337,5,118,0,0,337,338,5,111,0,0,338,
        339,5,108,0,0,339,340,5,117,0,0,340,341,5,109,0,0,341,342,5,101,
        0,0,342,818,5,115,0,0,343,344,5,101,0,0,344,345,5,110,0,0,345,346,
        5,97,0,0,346,347,5,98,0,0,347,348,5,108,0,0,348,349,5,101,0,0,349,
        350,5,95,0,0,350,351,5,97,0,0,351,352,5,110,0,0,352,353,5,100,0,
        0,353,354,5,95,0,0,354,355,5,108,0,0,355,356,5,111,0,0,356,357,5,
        99,0,0,357,358,5,107,0,0,358,359,5,95,0,0,359,360,5,118,0,0,360,
        361,5,109,0,0,361,818,5,120,0,0,362,363,5,101,0,0,363,364,5,110,
        0,0,364,365,5,97,0,0,365,366,5,98,0,0,366,367,5,108,0,0,367,368,
        5,101,0,0,368,369,5,95,0,0,369,370,5,109,0,0,370,371,5,111,0,0,371,
        372,5,117,0,0,372,373,5,115,0,0,373,818,5,101,0,0,374,375,5,101,
        0,0,375,376,5,110,0,0,376,377,5,97,0,0,377,378,5,98,0,0,378,379,
        5,108,0,0,379,380,5,101,0,0,380,381,5,95,0,0,381,382,5,116,0,0,382,
        383,5,111,0,0,383,384,5,117,0,0,384,385,5,99,0,0,385,818,5,104,0,
        0,386,387,5,101,0,0,387,388,5,120,0,0,388,389,5,116,0,0,389,390,
        5,114,0,0,390,391,5,97,0,0,391,392,5,95,0,0,392,393,5,107,0,0,393,
        394,5,101,0,0,394,395,5,114,0,0,395,396,5,110,0,0,396,397,5,101,
        0,0,397,398,5,108,0,0,398,399,5,95,0,0,399,400,5,118,0,0,400,401,
        5,101,0,0,401,402,5,114,0,0,402,403,5,115,0,0,403,404,5,105,0,0,
        404,405,5,111,0,0,405,406,5,110,0,0,406,407,5,95,0,0,407,408,5,115,
        0,0,408,409,5,116,0,0,409,410,5,114,0,0,410,411,5,105,0,0,411,412,
        5,110,0,0,412,413,5,103,0,0,413,818,5,115,0,0,414,415,5,102,0,0,
        415,416,5,111,0,0,416,417,5,108,0,0,417,418,5,100,0,0,418,419,5,
        95,0,0,419,420,5,108,0,0,420,421,5,105,0,0,421,422,5,110,0,0,422,
        423,5,117,0,0,423,424,5,120,0,0,424,425,5,95,0,0,425,426,5,107,0,
        0,426,427,5,101,0,0,427,428,5,114,0,0,428,429,5,110,0,0,429,430,
        5,101,0,0,430,431,5,108,0,0,431,818,5,115,0,0,432,433,5,102,0,0,
        433,434,5,111,0,0,434,435,5,108,0,0,435,436,5,108,0,0,436,437,5,
        111,0,0,437,438,5,119,0,0,438,439,5,95,0,0,439,440,5,115,0,0,440,
        441,5,121,0,0,441,442,5,109,0,0,442,443,5,108,0,0,443,444,5,105,
        0,0,444,445,5,110,0,0,445,446,5,107,0,0,446,818,5,115,0,0,447,448,
        5,102,0,0,448,449,5,111,0,0,449,450,5,110,0,0,450,818,5,116,0,0,
        451,452,5,104,0,0,452,453,5,105,0,0,453,454,5,100,0,0,454,455,5,
        101,0,0,455,456,5,117,0,0,456,818,5,105,0,0,457,458,5,105,0,0,458,
        459,5,99,0,0,459,460,5,111,0,0,460,461,5,110,0,0,461,462,5,115,0,
        0,462,463,5,95,0,0,463,464,5,100,0,0,464,465,5,105,0,0,465,818,5,
        114,0,0,466,467,5,108,0,0,467,468,5,105,0,0,468,469,5,110,0,0,469,
        470,5,117,0,0,470,471,5,120,0,0,471,472,5,95,0,0,472,473,5,112,0,
        0,473,474,5,114,0,0,474,475,5,101,0,0,475,476,5,102,0,0,476,477,
        5,105,0,0,477,478,5,120,0,0,478,479,5,101,0,0,479,818,5,115,0,0,
        480,481,5,108,0,0,481,482,5,111,0,0,482,483,5,103,0,0,483,484,5,
        95,0,0,484,485,5,108,0,0,485,486,5,101,0,0,486,487,5,118,0,0,487,
        488,5,101,0,0,488,818,5,108,0,0,489,490,5,109,0,0,490,491,5,97,0,
        0,491,492,5,120,0,0,492,493,5,95,0,0,493,494,5,116,0,0,494,495,5,
        97,0,0,495,496,5,103,0,0,496,818,5,115,0,0,497,498,5,109,0,0,498,
        499,5,111,0,0,499,500,5,117,0,0,500,501,5,115,0,0,501,502,5,101,
        0,0,502,503,5,95,0,0,503,504,5,115,0,0,504,505,5,105,0,0,505,506,
        5,122,0,0,506,818,5,101,0,0,507,508,5,109,0,0,508,509,5,111,0,0,
        509,510,5,117,0,0,510,511,5,115,0,0,511,512,5,101,0,0,512,513,5,
        95,0,0,513,514,5,115,0,0,514,515,5,112,0,0,515,516,5,101,0,0,516,
        517,5,101,0,0,517,818,5,100,0,0,518,519,5,114,0,0,519,520,5,101,
        0,0,520,521,5,115,0,0,521,522,5,111,0,0,522,523,5,108,0,0,523,524,
        5,117,0,0,524,525,5,116,0,0,525,526,5,105,0,0,526,527,5,111,0,0,
        527,818,5,110,0,0,528,529,5,115,0,0,529,530,5,99,0,0,530,531,5,97,
        0,0,531,532,5,110,0,0,532,533,5,95,0,0,533,534,5,97,0,0,534,535,
        5,108,0,0,535,536,5,108,0,0,536,537,5,95,0,0,537,538,5,108,0,0,538,
        539,5,105,0,0,539,540,5,110,0,0,540,541,5,117,0,0,541,542,5,120,
        0,0,542,543,5,95,0,0,543,544,5,107,0,0,544,545,5,101,0,0,545,546,
        5,114,0,0,546,547,5,110,0,0,547,548,5,101,0,0,548,549,5,108,0,0,
        549,818,5,115,0,0,550,551,5,115,0,0,551,552,5,99,0,0,552,553,5,97,
        0,0,553,554,5,110,0,0,554,555,5,95,0,0,555,556,5,100,0,0,556,557,
        5,101,0,0,557,558,5,108,0,0,558,559,5,97,0,0,559,818,5,121,0,0,560,
        561,5,115,0,0,561,562,5,99,0,0,562,563,5,97,0,0,563,564,5,110,0,
        0,564,565,5,95,0,0,565,566,5,100,0,0,566,567,5,114,0,0,567,568,5,
        105,0,0,568,569,5,118,0,0,569,570,5,101,0,0,570,571,5,114,0,0,571,
        572,5,95,0,0,572,573,5,100,0,0,573,574,5,105,0,0,574,575,5,114,0,
        0,575,818,5,115,0,0,576,577,5,115,0,0,577,578,5,99,0,0,578,579,5,
        97,0,0,579,580,5,110,0,0,580,581,5,102,0,0,581,582,5,111,0,0,582,
        818,5,114,0,0,583,584,5,115,0,0,584,585,5,99,0,0,585,586,5,114,0,
        0,586,587,5,101,0,0,587,588,5,101,0,0,588,589,5,110,0,0,589,590,
        5,115,0,0,590,591,5,97,0,0,591,592,5,118,0,0,592,593,5,101,0,0,593,
        818,5,114,0,0,594,595,5,115,0,0,595,596,5,101,0,0,596,597,5,108,
        0,0,597,598,5,101,0,0,598,599,5,99,0,0,599,600,5,116,0,0,600,601,
        5,105,0,0,601,602,5,111,0,0,602,603,5,110,0,0,603,604,5,95,0,0,604,
        605,5,98,0,0,605,606,5,105,0,0,606,818,5,103,0,0,607,608,5,115,0,
        0,608,609,5,101,0,0,609,610,5,108,0,0,610,611,5,101,0,0,611,612,
        5,99,0,0,612,613,5,116,0,0,613,614,5,105,0,0,614,615,5,111,0,0,615,
        616,5,110,0,0,616,617,5,95,0,0,617,618,5,115,0,0,618,619,5,109,0,
        0,619,620,5,97,0,0,620,621,5,108,0,0,621,818,5,108,0,0,622,623,5,
        115,0,0,623,624,5,104,0,0,624,625,5,111,0,0,625,626,5,119,0,0,626,
        627,5,116,0,0,627,628,5,111,0,0,628,629,5,111,0,0,629,630,5,108,
        0,0,630,818,5,115,0,0,631,632,5,115,0,0,632,633,5,104,0,0,633,634,
        5,117,0,0,634,635,5,116,0,0,635,636,5,100,0,0,636,637,5,111,0,0,
        637,638,5,119,0,0,638,639,5,110,0,0,639,640,5,95,0,0,640,641,5,97,
        0,0,641,642,5,102,0,0,642,643,5,116,0,0,643,644,5,101,0,0,644,645,
        5,114,0,0,645,646,5,95,0,0,646,647,5,116,0,0,647,648,5,105,0,0,648,
        649,5,109,0,0,649,650,5,101,0,0,650,651,5,111,0,0,651,652,5,117,
        0,0,652,818,5,116,0,0,653,654,5,115,0,0,654,655,5,109,0,0,655,656,
        5,97,0,0,656,657,5,108,0,0,657,658,5,108,0,0,658,659,5,95,0,0,659,
        660,5,105,0,0,660,661,5,99,0,0,661,662,5,111,0,0,662,663,5,110,0,
        0,663,664,5,95,0,0,664,665,5,115,0,0,665,666,5,105,0,0,666,667,5,
        122,0,0,667,818,5,101,0,0,668,669,5,115,0,0,669,670,5,112,0,0,670,
        671,5,111,0,0,671,672,5,111,0,0,672,673,5,102,0,0,673,674,5,95,0,
        0,674,675,5,111,0,0,675,676,5,115,0,0,676,677,5,120,0,0,677,678,
        5,95,0,0,678,679,5,118,0,0,679,680,5,101,0,0,680,681,5,114,0,0,681,
        682,5,115,0,0,682,683,5,105,0,0,683,684,5,111,0,0,684,818,5,110,
        0,0,685,686,5,115,0,0,686,687,5,117,0,0,687,688,5,112,0,0,688,689,
        5,112,0,0,689,690,5,111,0,0,690,691,5,114,0,0,691,692,5,116,0,0,
        692,693,5,95,0,0,693,694,5,103,0,0,694,695,5,122,0,0,695,696,5,105,
        0,0,696,697,5,112,0,0,697,698,5,112,0,0,698,699,5,101,0,0,699,700,
        5,100,0,0,700,701,5,95,0,0,701,702,5,108,0,0,702,703,5,111,0,0,703,
        704,5,97,0,0,704,705,5,100,0,0,705,706,5,101,0,0,706,707,5,114,0,
        0,707,818,5,115,0,0,708,709,5,116,0,0,709,710,5,101,0,0,710,711,
        5,120,0,0,711,712,5,116,0,0,712,713,5,109,0,0,713,714,5,111,0,0,
        714,715,5,100,0,0,715,818,5,101,0,0,716,717,5,116,0,0,717,718,5,
        101,0,0,718,719,5,120,0,0,719,720,5,116,0,0,720,721,5,111,0,0,721,
        722,5,110,0,0,722,723,5,108,0,0,723,818,5,121,0,0,724,725,5,116,
        0,0,725,726,5,105,0,0,726,727,5,109,0,0,727,728,5,101,0,0,728,729,
        5,111,0,0,729,730,5,117,0,0,730,818,5,116,0,0,731,732,5,117,0,0,
        732,733,5,101,0,0,733,734,5,102,0,0,734,735,5,105,0,0,735,736,5,
        95,0,0,736,737,5,100,0,0,737,738,5,101,0,0,738,739,5,101,0,0,739,
        740,5,112,0,0,740,741,5,95,0,0,741,742,5,108,0,0,742,743,5,101,0,
        0,743,744,5,103,0,0,744,745,5,97,0,0,745,746,5,99,0,0,746,747,5,
        121,0,0,747,748,5,95,0,0,748,749,5,115,0,0,749,750,5,99,0,0,750,
        751,5,97,0,0,751,818,5,110,0,0,752,753,5,117,0,0,753,754,5,115,0,
        0,754,755,5,101,0,0,755,756,5,95,0,0,756,757,5,103,0,0,757,758,5,
        114,0,0,758,759,5,97,0,0,759,760,5,112,0,0,760,761,5,104,0,0,761,
        762,5,105,0,0,762,763,5,99,0,0,763,764,5,115,0,0,764,765,5,95,0,
        0,765,766,5,102,0,0,766,767,5,111,0,0,767,818,5,114,0,0,768,769,
        5,117,0,0,769,770,5,115,0,0,770,771,5,101,0,0,771,772,5,95,0,0,772,
        773,5,110,0,0,773,774,5,118,0,0,774,775,5,114,0,0,775,776,5,97,0,
        0,776,818,5,109,0,0,777,778,5,119,0,0,778,779,5,105,0,0,779,780,
        5,110,0,0,780,781,5,100,0,0,781,782,5,111,0,0,782,783,5,119,0,0,
        783,784,5,115,0,0,784,785,5,95,0,0,785,786,5,114,0,0,786,787,5,101,
        0,0,787,788,5,99,0,0,788,789,5,111,0,0,789,790,5,118,0,0,790,791,
        5,101,0,0,791,792,5,114,0,0,792,793,5,121,0,0,793,794,5,95,0,0,794,
        795,5,102,0,0,795,796,5,105,0,0,796,797,5,108,0,0,797,798,5,101,
        0,0,798,818,5,115,0,0,799,800,5,119,0,0,800,801,5,114,0,0,801,802,
        5,105,0,0,802,803,5,116,0,0,803,804,5,101,0,0,804,805,5,95,0,0,805,
        806,5,115,0,0,806,807,5,121,0,0,807,808,5,115,0,0,808,809,5,116,
        0,0,809,810,5,101,0,0,810,811,5,109,0,0,811,812,5,100,0,0,812,813,
        5,95,0,0,813,814,5,118,0,0,814,815,5,97,0,0,815,816,5,114,0,0,816,
        818,5,115,0,0,817,93,1,0,0,0,817,107,1,0,0,0,817,122,1,0,0,0,817,
        128,1,0,0,0,817,140,1,0,0,0,817,153,1,0,0,0,817,163,1,0,0,0,817,
        180,1,0,0,0,817,195,1,0,0,0,817,211,1,0,0,0,817,230,1,0,0,0,817,
        246,1,0,0,0,817,264,1,0,0,0,817,278,1,0,0,0,817,293,1,0,0,0,817,
        311,1,0,0,0,817,326,1,0,0,0,817,343,1,0,0,0,817,362,1,0,0,0,817,
        374,1,0,0,0,817,386,1,0,0,0,817,414,1,0,0,0,817,432,1,0,0,0,817,
        447,1,0,0,0,817,451,1,0,0,0,817,457,1,0,0,0,817,466,1,0,0,0,817,
        480,1,0,0,0,817,489,1,0,0,0,817,497,1,0,0,0,817,507,1,0,0,0,817,
        518,1,0,0,0,817,528,1,0,0,0,817,550,1,0,0,0,817,560,1,0,0,0,817,
        576,1,0,0,0,817,583,1,0,0,0,817,594,1,0,0,0,817,607,1,0,0,0,817,
        622,1,0,0,0,817,631,1,0,0,0,817,653,1,0,0,0,817,668,1,0,0,0,817,
        685,1,0,0,0,817,708,1,0,0,0,817,716,1,0,0,0,817,724,1,0,0,0,817,
        731,1,0,0,0,817,752,1,0,0,0,817,768,1,0,0,0,817,777,1,0,0,0,817,
        799,1,0,0,0,818,822,1,0,0,0,819,821,8,1,0,0,820,819,1,0,0,0,821,
        824,1,0,0,0,822,820,1,0,0,0,822,823,1,0,0,0,823,826,1,0,0,0,824,
        822,1,0,0,0,825,827,3,4,1,0,826,825,1,0,0,0,826,827,1,0,0,0,827,
        828,1,0,0,0,828,829,6,4,0,0,829,11,1,0,0,0,830,833,3,14,6,0,831,
        833,3,16,7,0,832,830,1,0,0,0,832,831,1,0,0,0,833,13,1,0,0,0,834,
        835,5,109,0,0,835,836,5,101,0,0,836,837,5,110,0,0,837,838,5,117,
        0,0,838,839,5,101,0,0,839,840,5,110,0,0,840,841,5,116,0,0,841,842,
        5,114,0,0,842,843,5,121,0,0,843,15,1,0,0,0,844,845,5,115,0,0,845,
        846,5,117,0,0,846,847,5,98,0,0,847,848,5,109,0,0,848,849,5,101,0,
        0,849,850,5,110,0,0,850,851,5,117,0,0,851,852,5,101,0,0,852,853,
        5,110,0,0,853,854,5,116,0,0,854,855,5,114,0,0,855,856,5,121,0,0,
        856,17,1,0,0,0,857,858,5,118,0,0,858,859,5,111,0,0,859,860,5,108,
        0,0,860,861,5,117,0,0,861,862,5,109,0,0,862,863,5,101,0,0,863,19,
        1,0,0,0,864,865,5,108,0,0,865,866,5,111,0,0,866,867,5,97,0,0,867,
        868,5,100,0,0,868,869,5,101,0,0,869,870,5,114,0,0,870,21,1,0,0,0,
        871,872,5,105,0,0,872,873,5,110,0,0,873,874,5,105,0,0,874,875,5,
        116,0,0,875,876,5,114,0,0,876,877,5,100,0,0,877,23,1,0,0,0,878,879,
        5,105,0,0,879,880,5,99,0,0,880,881,5,111,0,0,881,882,5,110,0,0,882,
        25,1,0,0,0,883,884,5,111,0,0,884,885,5,115,0,0,885,886,5,116,0,0,
        886,887,5,121,0,0,887,888,5,112,0,0,888,889,5,101,0,0,889,890,1,
        0,0,0,890,891,3,2,0,0,891,892,1,0,0,0,892,893,6,12,1,0,893,27,1,
        0,0,0,894,895,5,103,0,0,895,896,5,114,0,0,896,897,5,97,0,0,897,898,
        5,112,0,0,898,899,5,104,0,0,899,900,5,105,0,0,900,901,5,99,0,0,901,
        902,5,115,0,0,902,903,1,0,0,0,903,904,3,2,0,0,904,905,1,0,0,0,905,
        906,6,13,1,0,906,29,1,0,0,0,907,908,5,111,0,0,908,909,5,112,0,0,
        909,910,5,116,0,0,910,911,5,105,0,0,911,912,5,111,0,0,912,913,5,
        110,0,0,913,914,5,115,0,0,914,31,1,0,0,0,915,916,5,97,0,0,916,917,
        5,100,0,0,917,918,5,100,0,0,918,919,5,95,0,0,919,920,5,111,0,0,920,
        921,5,112,0,0,921,922,5,116,0,0,922,923,5,105,0,0,923,924,5,111,
        0,0,924,925,5,110,0,0,925,926,5,115,0,0,926,33,1,0,0,0,927,928,5,
        102,0,0,928,929,5,105,0,0,929,930,5,114,0,0,930,931,5,109,0,0,931,
        932,5,119,0,0,932,933,5,97,0,0,933,934,5,114,0,0,934,935,5,101,0,
        0,935,936,5,95,0,0,936,937,5,98,0,0,937,938,5,111,0,0,938,939,5,
        111,0,0,939,940,5,116,0,0,940,941,5,110,0,0,941,942,5,117,0,0,942,
        943,5,109,0,0,943,35,1,0,0,0,944,945,5,100,0,0,945,946,5,105,0,0,
        946,947,5,115,0,0,947,948,5,97,0,0,948,949,5,98,0,0,949,950,5,108,
        0,0,950,951,5,101,0,0,951,952,5,100,0,0,952,37,1,0,0,0,953,954,5,
        105,0,0,954,955,5,110,0,0,955,956,5,99,0,0,956,957,5,108,0,0,957,
        958,5,117,0,0,958,959,5,100,0,0,959,960,5,101,0,0,960,39,1,0,0,0,
        961,962,5,123,0,0,962,41,1,0,0,0,963,964,5,125,0,0,964,43,1,0,0,
        0,965,967,3,46,22,0,966,965,1,0,0,0,967,968,1,0,0,0,968,966,1,0,
        0,0,968,969,1,0,0,0,969,45,1,0,0,0,970,971,7,2,0,0,971,47,1,0,0,
        0,972,976,3,50,24,0,973,976,3,52,25,0,974,976,3,54,26,0,975,972,
        1,0,0,0,975,973,1,0,0,0,975,974,1,0,0,0,976,49,1,0,0,0,977,979,5,
        39,0,0,978,980,8,1,0,0,979,978,1,0,0,0,980,981,1,0,0,0,981,979,1,
        0,0,0,981,982,1,0,0,0,982,983,1,0,0,0,983,984,5,39,0,0,984,51,1,
        0,0,0,985,987,5,34,0,0,986,988,8,1,0,0,987,986,1,0,0,0,988,989,1,
        0,0,0,989,987,1,0,0,0,989,990,1,0,0,0,990,991,1,0,0,0,991,992,5,
        34,0,0,992,53,1,0,0,0,993,995,8,3,0,0,994,993,1,0,0,0,995,996,1,
        0,0,0,996,994,1,0,0,0,996,997,1,0,0,0,997,55,1,0,0,0,998,999,5,77,
        0,0,999,1000,5,97,0,0,1000,1001,5,99,0,0,1001,1002,5,79,0,0,1002,
        1024,5,83,0,0,1003,1004,5,76,0,0,1004,1005,5,105,0,0,1005,1006,5,
        110,0,0,1006,1007,5,117,0,0,1007,1024,5,120,0,0,1008,1009,5,69,0,
        0,1009,1010,5,76,0,0,1010,1011,5,73,0,0,1011,1012,5,76,0,0,1012,
        1024,5,79,0,0,1013,1014,5,87,0,0,1014,1015,5,105,0,0,1015,1016,5,
        110,0,0,1016,1017,5,100,0,0,1017,1018,5,111,0,0,1018,1019,5,119,
        0,0,1019,1024,5,115,0,0,1020,1021,5,88,0,0,1021,1022,5,79,0,0,1022,
        1024,5,77,0,0,1023,998,1,0,0,0,1023,1003,1,0,0,0,1023,1008,1,0,0,
        0,1023,1013,1,0,0,0,1023,1020,1,0,0,0,1024,1025,1,0,0,0,1025,1026,
        6,27,2,0,1026,57,1,0,0,0,1027,1028,5,111,0,0,1028,1033,5,110,0,0,
        1029,1030,5,111,0,0,1030,1031,5,102,0,0,1031,1033,5,102,0,0,1032,
        1027,1,0,0,0,1032,1029,1,0,0,0,1033,1034,1,0,0,0,1034,1035,6,28,
        2,0,1035,59,1,0,0,0,18,0,1,63,68,75,85,89,817,822,826,832,968,975,
        981,989,996,1023,1032,3,6,0,0,5,1,0,4,0,0
    ]

class RefindConfigLexer(Lexer):

    atn = ATNDeserializer().deserialize(serializedATN())

    decisionsToDFA = [ DFA(ds, i) for i, ds in enumerate(atn.decisionToState) ]

    STRICT_PARAMETER_MODE = 1

    WHITESPACE = 1
    NEWLINE = 2
    EMPTY = 3
    COMMENT = 4
    IGNORED_OPTION = 5
    MENU_ENTRY = 6
    VOLUME = 7
    LOADER = 8
    INITRD = 9
    ICON = 10
    OS_TYPE = 11
    GRAPHICS = 12
    BOOT_OPTIONS = 13
    ADD_BOOT_OPTIONS = 14
    FIRMWARE_BOOTNUM = 15
    DISABLED = 16
    INCLUDE = 17
    OPEN_BRACE = 18
    CLOSE_BRACE = 19
    HEX_INTEGER = 20
    STRING = 21
    OS_TYPE_PARAMETER = 22
    GRAPHICS_PARAMETER = 23

    channelNames = [ u"DEFAULT_TOKEN_CHANNEL", u"HIDDEN" ]

    modeNames = [ "DEFAULT_MODE", "STRICT_PARAMETER_MODE" ]

    literalNames = [ "<INVALID>",
            "'volume'", "'loader'", "'initrd'", "'icon'", "'options'", "'add_options'", 
            "'firmware_bootnum'", "'disabled'", "'include'", "'{'", "'}'" ]

    symbolicNames = [ "<INVALID>",
            "WHITESPACE", "NEWLINE", "EMPTY", "COMMENT", "IGNORED_OPTION", 
            "MENU_ENTRY", "VOLUME", "LOADER", "INITRD", "ICON", "OS_TYPE", 
            "GRAPHICS", "BOOT_OPTIONS", "ADD_BOOT_OPTIONS", "FIRMWARE_BOOTNUM", 
            "DISABLED", "INCLUDE", "OPEN_BRACE", "CLOSE_BRACE", "HEX_INTEGER", 
            "STRING", "OS_TYPE_PARAMETER", "GRAPHICS_PARAMETER" ]

    ruleNames = [ "WHITESPACE", "NEWLINE", "EMPTY", "COMMENT", "IGNORED_OPTION", 
                  "MENU_ENTRY", "MAIN_MENU_ENTRY", "SUB_MENU_ENTRY", "VOLUME", 
                  "LOADER", "INITRD", "ICON", "OS_TYPE", "GRAPHICS", "BOOT_OPTIONS", 
                  "ADD_BOOT_OPTIONS", "FIRMWARE_BOOTNUM", "DISABLED", "INCLUDE", 
                  "OPEN_BRACE", "CLOSE_BRACE", "HEX_INTEGER", "HEX_DIGIT", 
                  "STRING", "SINGLE_QUOTED_STRING", "DOUBLE_QUOTED_STRING", 
                  "UNQUOTED_STRING", "OS_TYPE_PARAMETER", "GRAPHICS_PARAMETER" ]

    grammarFileName = "RefindConfigLexer.g4"

    def __init__(self, input=None, output:TextIO = sys.stdout):
        super().__init__(input, output)
        self.checkVersion("4.13.1")
        self._interp = LexerATNSimulator(self, self.atn, self.decisionsToDFA, PredictionContextCache())
        self._actions = None
        self._predicates = None


