{"antlr4.generation": {"alternativeJar": "C:\\Users\\<USER>\\Projects\\Python\\antlr-4.13.1-complete.jar", "language": "Python3", "listeners": false, "mode": "external", "visitors": true}, "cSpell.words": ["antlr", "bootable", "Bootnum", "Btrfs", "btrfsutil", "bytecode", "ELILO", "EPERM", "errno", "findmnt", "fstype", "getpid", "getuid", "groupby", "ICNS", "IMODE", "INITRD", "inplace", "ismethod", "itertools", "journalctl", "levelno", "Lsblk", "menuentry", "mtab", "nargs", "nodeps", "nofsroot", "ostype", "otime", "partuuid", "pidfile", "pidname", "PTABLE", "pttype", "p<PERSON>uid", "pycache", "pylint", "pyproject", "PYTHONPATH", "refind", "rootflags", "setuptools", "SPDX", "submenuentry", "Subobject", "suboption", "subvol", "subvolid", "subvolume", "<PERSON><PERSON><PERSON><PERSON>", "vfat"], "code-runner.clearPreviousOutput": true, "code-runner.runInTerminal": true, "python.analysis.completeFunctionParens": true, "python.analysis.diagnosticMode": "workspace", "python.analysis.extraPaths": ["src/refind_btrfs"], "python.defaultInterpreterPath": "${workspaceFolder}/.venv/bin/python", "python.envFile": "${workspaceFolder}/.env", "python.formatting.provider": "black", "python.languageServer": "<PERSON><PERSON><PERSON>", "python.linting.enabled": true, "python.linting.mypyEnabled": true, "python.linting.mypyPath": "${workspaceFolder}/.venv/bin/mypy", "python.linting.pylintEnabled": true, "python.linting.pylintPath": "${workspaceFolder}/.venv/bin/pylint", "search.exclude": {"**/.venv": true}, "terminal.integrated.env.linux": {"PYTHONPATH": "${env:PYTHONPATH}:${workspaceFolder}/src"}}