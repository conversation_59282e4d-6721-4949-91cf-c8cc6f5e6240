
timeout 5
log_level 2
enable_mouse
mouse_speed 8
#also_scan_dirs +,boot,/@/boot
#extra_kernel_version_strings "linux-hardened,linux-rt-lts,linux-zen,linux-cachyos,linux-lts,linux-rt,linux"
#extra_kernel_version_strings "linux-hardened,linux-rt-lts,linux-zen,linux-cachyos,linux-lts,linux-rt,linux"
default_selection +
#fold_linux_kernels false
scan_all_linux_kernels false

menuentry "Arch Linux" {
    icon     /EFI/refind/icons/os_arch.png
    volume   5eee5dc4-2aab-4cee-b5d7-2558c2b4b71d
    loader   /boot/vmlinuz-linux-cachyos
    initrd   /boot/initramfs-linux-cachyos.img
    options  "root=UUID=d7087759-aff3-41bf-9ab5-e7de33f1019b rw rootflags=subvol=@ zswap.enabled=0 rootfstype=btrfs i915.force_probe=!7d55 xe.force_probe=7d55 intel_pstate=active xe.enable_psr=1 nvidia_drm.modeset=1 loglevel=3"
    
    submenuentry "Boot using fallback initramfs" {
        initrd /boot/initramfs-linux-cachyos-fallback.img
    }
    submenuentry "Boot Zen kernel" {
        loader /boot/vmlinuz-linux-zen
        initrd /boot/initramfs-linux-zen.img
    }
    submenuentry "Boot Zen kernel using fallback initramfs" {
        loader /boot/vmlinuz-linux-zen
        initrd /boot/initramfs-linux-zen-fallback.img
    }
}

include btrfs-snapshot-stanzas/5eee5dc4-2aab-4cee-b5d7-2558c2b4b71d_vmlinuz-linux-cachyos.conf
