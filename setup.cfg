[metadata]
name = refind_btrfs
version = 0.6.3
description = Generate rEFInd manual boot stanzas from Btrfs snapshots
long_description = file: README.md
keywords = rEFInd, btrfs
url = https://github.com/Venom1991/refind-btrfs
author = <PERSON><PERSON>
author_email = <EMAIL>
maintainer = <PERSON><PERSON>
maintainer_email = <EMAIL>
license = GNU General Public License v3 or later (GPLv3+)
license_file = LICENSE.txt
platforms = Linux
classifiers = 
        Development Status :: 4 - Beta
        Intended Audience :: End Users/Desktop
        License :: OSI Approved :: GNU General Public License v3 or later (GPLv3+)
        Natural Language :: English
        Operating System :: POSIX :: Linux
        Programming Language :: Python :: 3.11
        Topic :: System :: Boot

[options]
package_dir =
    =src
packages = find:
include_package_data = True
install_requires =
    antlr4-python3-runtime
    injector
    more-itertools
    pid
    semantic-version
    systemd-python
    tomlkit
    transitions
    typeguard
    watchdog
python_requires = >= 3.11

[options.extras_require]
custom_icon = Pillow

[options.entry_points]
console_scripts =
    refind-btrfs = refind_btrfs:main

[options.packages.find]
where = src

[bdist_wheel]
universal = False
